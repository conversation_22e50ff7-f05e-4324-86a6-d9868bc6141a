# 多域架构设计指南

## 🏛️ 多域架构概述

当系统包含多个业务域时，我们需要重新组织代码结构以支持多个有界上下文（Bounded Context）。

## 📁 推荐的多域目录结构

```
smart-campus/client-service/
├── cmd/                              # 应用程序入口点
│   ├── server/                       # HTTP服务器入口
│   └── cli/                          # CLI工具入口
├── internal/                         # 内部应用代码
│   ├── domain/                       # 所有域的集合
│   │   ├── user/                     # 用户域
│   │   │   ├── entity/               # 用户实体
│   │   │   ├── valueobject/          # 用户值对象
│   │   │   ├── service/              # 用户领域服务
│   │   │   └── repository/           # 用户仓储接口
│   │   ├── course/                   # 课程域
│   │   │   ├── entity/               # 课程实体
│   │   │   ├── valueobject/          # 课程值对象
│   │   │   ├── service/              # 课程领域服务
│   │   │   └── repository/           # 课程仓储接口
│   │   ├── enrollment/               # 选课域
│   │   │   ├── entity/               # 选课实体
│   │   │   ├── valueobject/          # 选课值对象
│   │   │   ├── service/              # 选课领域服务
│   │   │   └── repository/           # 选课仓储接口
│   │   └── shared/                   # 跨域共享
│   │       ├── entity/               # 共享实体
│   │       ├── valueobject/          # 共享值对象
│   │       └── service/              # 共享领域服务
│   ├── application/                  # 应用层（按域组织）
│   │   ├── user/                     # 用户应用服务
│   │   │   ├── service/              # 用户应用服务
│   │   │   ├── command/              # 用户命令处理器
│   │   │   └── query/                # 用户查询处理器
│   │   ├── course/                   # 课程应用服务
│   │   │   ├── service/              # 课程应用服务
│   │   │   ├── command/              # 课程命令处理器
│   │   │   └── query/                # 课程查询处理器
│   │   ├── enrollment/               # 选课应用服务
│   │   │   ├── service/              # 选课应用服务
│   │   │   ├── command/              # 选课命令处理器
│   │   │   └── query/                # 选课查询处理器
│   │   └── shared/                   # 跨域应用服务
│   │       └── service/              # 共享应用服务
│   ├── infrastructure/               # 基础设施层
│   │   ├── persistence/              # 数据持久化
│   │   │   ├── user/                 # 用户仓储实现
│   │   │   ├── course/               # 课程仓储实现
│   │   │   ├── enrollment/           # 选课仓储实现
│   │   │   └── shared/               # 共享持久化组件
│   │   ├── external/                 # 外部服务
│   │   ├── messaging/                # 消息队列
│   │   └── container/                # 依赖注入容器
│   └── interfaces/                   # 接口适配器层
│       ├── http/                     # HTTP接口
│       │   ├── user/                 # 用户控制器
│       │   ├── course/               # 课程控制器
│       │   ├── enrollment/           # 选课控制器
│       │   └── shared/               # 共享HTTP组件
│       ├── grpc/                     # gRPC接口
│       │   ├── user/                 # 用户gRPC服务
│       │   ├── course/               # 课程gRPC服务
│       │   └── enrollment/           # 选课gRPC服务
│       └── cli/                      # CLI接口
│           ├── user/                 # 用户CLI命令
│           ├── course/               # 课程CLI命令
│           └── enrollment/           # 选课CLI命令
├── pkg/                              # 公共库（跨服务共享）
│   ├── config/                       # 配置管理
│   ├── logger/                       # 日志
│   ├── errors/                       # 错误处理
│   └── utils/                        # 工具函数
├── api/                              # API定义（对外暴露）
│   ├── http/                         # HTTP API文档
│   ├── grpc/                         # gRPC Proto文件
│   └── openapi/                      # OpenAPI规范
├── config/                           # 配置文件
├── docs/                             # 文档
├── scripts/                          # 脚本
└── test/                             # 测试
    ├── integration/                  # 集成测试
    │   ├── user/                     # 用户集成测试
    │   ├── course/                   # 课程集成测试
    │   └── enrollment/               # 选课集成测试
    └── e2e/                          # 端到端测试
```

## 🎯 域的划分原则

### 1. 有界上下文识别
- **用户域 (User Domain)**: 用户管理、认证、权限
- **课程域 (Course Domain)**: 课程信息、课程管理
- **选课域 (Enrollment Domain)**: 选课逻辑、成绩管理
- **通知域 (Notification Domain)**: 消息通知、邮件发送

### 2. 域间关系
- **共享内核 (Shared Kernel)**: 放在 `shared/` 目录
- **客户-供应商 (Customer-Supplier)**: 通过接口定义依赖
- **防腐层 (Anti-Corruption Layer)**: 在应用层处理域间转换

## 🔄 域间通信

### 1. 同步通信
```go
// 在应用服务中协调多个域
type EnrollmentApplicationService struct {
    enrollmentRepo repository.EnrollmentRepository
    userService    user.UserDomainService      // 跨域调用
    courseService  course.CourseDomainService  // 跨域调用
}
```

### 2. 异步通信
```go
// 通过事件进行域间通信
type UserCreatedEvent struct {
    UserID uuid.UUID
    Email  string
    Name   string
}

// 其他域监听事件
type NotificationEventHandler struct {
    notificationService notification.NotificationService
}

func (h *NotificationEventHandler) Handle(event UserCreatedEvent) error {
    return h.notificationService.SendWelcomeEmail(event.Email, event.Name)
}
```

## 📦 包命名约定

### 1. 域包命名
```go
// 用户域
import "smart-campus/client-service/internal/domain/user/entity"
import "smart-campus/client-service/internal/application/user/service"

// 课程域
import "smart-campus/client-service/internal/domain/course/entity"
import "smart-campus/client-service/internal/application/course/service"
```

### 2. 避免循环依赖
```go
// ❌ 错误：域之间直接依赖
package user
import "smart-campus/client-service/internal/domain/course"

// ✅ 正确：通过接口和应用层协调
package user
type CourseService interface {
    GetCourse(id uuid.UUID) (*Course, error)
}
```

## 🏗️ 实现步骤

### 1. 识别域边界
- 分析业务需求
- 识别聚合根
- 定义有界上下文

### 2. 重构现有代码
- 按域重新组织目录
- 提取共享组件
- 定义域间接口

### 3. 实现域间通信
- 定义事件
- 实现事件处理器
- 配置消息路由

## 🎨 最佳实践

### 1. 域的独立性
- 每个域应该能够独立开发和部署
- 最小化域间依赖
- 使用接口而不是具体实现

### 2. 共享组件管理
- 谨慎使用共享组件
- 优先复制而不是共享
- 版本化共享接口

### 3. 测试策略
- 每个域独立测试
- 域间集成测试
- 端到端业务流程测试

## 🚀 迁移策略

### 阶段1：重新组织目录结构
1. 创建域目录
2. 移动现有代码
3. 更新导入路径

### 阶段2：提取共享组件
1. 识别跨域共享的代码
2. 移动到shared目录
3. 定义清晰的接口

### 阶段3：实现域间通信
1. 定义域事件
2. 实现事件发布订阅
3. 重构跨域调用

这种多域架构为大型系统提供了更好的可维护性和可扩展性，每个域可以独立演进，同时保持整体系统的一致性。
