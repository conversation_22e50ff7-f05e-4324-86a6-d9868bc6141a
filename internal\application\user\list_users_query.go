package query

import (
	"context"

	"smart-campus/client-service/internal/domain/repository"
)

// ListUsersQuery represents a query to list users
type ListUsersQuery struct {
	Offset int `json:"offset" validate:"min=0"`
	Limit  int `json:"limit" validate:"min=1,max=100"`
}

// ListUsersQueryResult represents the result of listing users
type ListUsersQueryResult struct {
	Users      []UserQueryResult `json:"users"`
	TotalCount int64             `json:"total_count"`
	Offset     int               `json:"offset"`
	Limit      int               `json:"limit"`
}

// ListUsersQueryHandler handles the list users query
type ListUsersQueryHandler struct {
	userRepo repository.UserRepository
}

// NewListUsersQueryHandler creates a new list users query handler
func NewListUsersQueryHandler(userRepo repository.UserRepository) *ListUsersQueryHandler {
	return &ListUsersQueryHandler{
		userRepo: userRepo,
	}
}

// <PERSON>le handles the list users query
func (h *ListUsersQueryHandler) Handle(ctx context.Context, query ListUsersQuery) (*ListUsersQueryResult, error) {
	// Set default values
	if query.Limit == 0 {
		query.Limit = 20
	}
	
	// Get users
	users, err := h.userRepo.FindAll(ctx, query.Offset, query.Limit)
	if err != nil {
		return nil, err
	}
	
	// Get total count
	totalCount, err := h.userRepo.Count(ctx)
	if err != nil {
		return nil, err
	}
	
	// Convert to query results
	userResults := make([]UserQueryResult, len(users))
	for i, user := range users {
		var status string
		switch user.Status() {
		case 0: // UserStatusActive
			status = "active"
		case 1: // UserStatusInactive
			status = "inactive"
		case 2: // UserStatusSuspended
			status = "suspended"
		default:
			status = "unknown"
		}
		
		userResults[i] = UserQueryResult{
			ID:        user.ID(),
			Email:     user.Email().Value(),
			Name:      user.Name(),
			Status:    status,
			CreatedAt: user.CreatedAt(),
			UpdatedAt: user.UpdatedAt(),
		}
	}
	
	return &ListUsersQueryResult{
		Users:      userResults,
		TotalCount: totalCount,
		Offset:     query.Offset,
		Limit:      query.Limit,
	}, nil
}
