package messaging

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// Event represents a domain event
type Event struct {
	ID          uuid.UUID   `json:"id"`
	Type        string      `json:"type"`
	AggregateID uuid.UUID   `json:"aggregate_id"`
	Data        interface{} `json:"data"`
	Timestamp   time.Time   `json:"timestamp"`
	Version     int         `json:"version"`
}

// EventPublisher defines the interface for publishing events
type EventPublisher interface {
	Publish(ctx context.Context, event Event) error
	PublishBatch(ctx context.Context, events []Event) error
}

// InMemoryEventPublisher is a simple in-memory event publisher for development
type InMemoryEventPublisher struct {
	subscribers map[string][]EventHandler
}

// EventHandler defines the interface for handling events
type EventHandler interface {
	Handle(ctx context.Context, event Event) error
}

// NewInMemoryEventPublisher creates a new in-memory event publisher
func NewInMemoryEventPublisher() *InMemoryEventPublisher {
	return &InMemoryEventPublisher{
		subscribers: make(map[string][]EventHandler),
	}
}

// Subscribe subscribes a handler to an event type
func (p *InMemoryEventPublisher) Subscribe(eventType string, handler EventHandler) {
	p.subscribers[eventType] = append(p.subscribers[eventType], handler)
}

// Publish publishes an event
func (p *InMemoryEventPublisher) Publish(ctx context.Context, event Event) error {
	handlers, exists := p.subscribers[event.Type]
	if !exists {
		return nil // No handlers for this event type
	}
	
	for _, handler := range handlers {
		if err := handler.Handle(ctx, event); err != nil {
			return fmt.Errorf("failed to handle event %s: %w", event.Type, err)
		}
	}
	
	return nil
}

// PublishBatch publishes multiple events
func (p *InMemoryEventPublisher) PublishBatch(ctx context.Context, events []Event) error {
	for _, event := range events {
		if err := p.Publish(ctx, event); err != nil {
			return err
		}
	}
	return nil
}

// NewEvent creates a new event
func NewEvent(eventType string, aggregateID uuid.UUID, data interface{}) Event {
	return Event{
		ID:          uuid.New(),
		Type:        eventType,
		AggregateID: aggregateID,
		Data:        data,
		Timestamp:   time.Now(),
		Version:     1,
	}
}

// ToJSON converts an event to JSON
func (e Event) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// FromJSON creates an event from JSON
func FromJSON(data []byte) (Event, error) {
	var event Event
	err := json.Unmarshal(data, &event)
	return event, err
}
