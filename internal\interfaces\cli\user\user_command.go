package cli

import (
	"context"
	"fmt"
	"strconv"

	"github.com/google/uuid"
	"github.com/spf13/cobra"
	"smart-campus/client-service/internal/application/command"
	"smart-campus/client-service/internal/application/service"
)

// UserCommand provides CLI commands for user operations
type UserCommand struct {
	userService *service.UserApplicationService
}

// NewUserCommand creates a new user command
func NewUserCommand(userService *service.UserApplicationService) *UserCommand {
	return &UserCommand{
		userService: userService,
	}
}

// GetCommand returns the user command
func (c *UserCommand) GetCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "user",
		Short: "User management commands",
		Long:  "Commands for managing users in the system",
	}

	cmd.AddCommand(c.createUserCommand())
	cmd.AddCommand(c.getUserCommand())
	cmd.AddCommand(c.updateUserCommand())
	cmd.AddCommand(c.listUsersCommand())

	return cmd
}

func (c *UserCommand) createUserCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "create [email] [name]",
		Short: "Create a new user",
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			email := args[0]
			name := args[1]

			createCmd := command.CreateUserCommand{
				Email: email,
				Name:  name,
			}

			result, err := c.userService.CreateUser(context.Background(), createCmd)
			if err != nil {
				return fmt.Errorf("failed to create user: %w", err)
			}

			fmt.Printf("User created successfully with ID: %s\n", result.UserID)
			return nil
		},
	}
}

func (c *UserCommand) getUserCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "get [user-id]",
		Short: "Get a user by ID",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			userID, err := uuid.Parse(args[0])
			if err != nil {
				return fmt.Errorf("invalid user ID: %w", err)
			}

			user, err := c.userService.GetUser(context.Background(), userID)
			if err != nil {
				return fmt.Errorf("failed to get user: %w", err)
			}

			fmt.Printf("User ID: %s\n", user.ID)
			fmt.Printf("Email: %s\n", user.Email)
			fmt.Printf("Name: %s\n", user.Name)
			fmt.Printf("Status: %s\n", user.Status)
			fmt.Printf("Created At: %s\n", user.CreatedAt.Format("2006-01-02 15:04:05"))
			fmt.Printf("Updated At: %s\n", user.UpdatedAt.Format("2006-01-02 15:04:05"))
			return nil
		},
	}
}

func (c *UserCommand) updateUserCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "update [user-id] [name]",
		Short: "Update a user's name",
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			userID, err := uuid.Parse(args[0])
			if err != nil {
				return fmt.Errorf("invalid user ID: %w", err)
			}

			name := args[1]

			updateCmd := command.UpdateUserCommand{
				UserID: userID,
				Name:   name,
			}

			if err := c.userService.UpdateUser(context.Background(), updateCmd); err != nil {
				return fmt.Errorf("failed to update user: %w", err)
			}

			fmt.Println("User updated successfully")
			return nil
		},
	}
}

func (c *UserCommand) listUsersCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "List users",
		RunE: func(cmd *cobra.Command, args []string) error {
			offsetStr, _ := cmd.Flags().GetString("offset")
			limitStr, _ := cmd.Flags().GetString("limit")

			offset, err := strconv.Atoi(offsetStr)
			if err != nil {
				return fmt.Errorf("invalid offset: %w", err)
			}

			limit, err := strconv.Atoi(limitStr)
			if err != nil {
				return fmt.Errorf("invalid limit: %w", err)
			}

			result, err := c.userService.ListUsers(context.Background(), offset, limit)
			if err != nil {
				return fmt.Errorf("failed to list users: %w", err)
			}

			fmt.Printf("Total users: %d\n", result.TotalCount)
			fmt.Printf("Showing %d users (offset: %d, limit: %d)\n\n", len(result.Users), result.Offset, result.Limit)

			for _, user := range result.Users {
				fmt.Printf("ID: %s | Email: %s | Name: %s | Status: %s\n",
					user.ID, user.Email, user.Name, user.Status)
			}

			return nil
		},
	}

	cmd.Flags().String("offset", "0", "Offset for pagination")
	cmd.Flags().String("limit", "20", "Limit for pagination")

	return cmd
}
