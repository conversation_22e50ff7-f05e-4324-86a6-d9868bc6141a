package main

import (
	"log"
	"os"

	"github.com/spf13/cobra"
	"smart-campus/client-service/internal/infrastructure/container"
	"smart-campus/client-service/internal/infrastructure/persistence"
	"smart-campus/client-service/pkg/config"
)

func main() {
	// Load configuration
	cfg, err := config.Load("./config")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database
	db, err := persistence.NewDatabase(persistence.DatabaseConfig{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Initialize dependencies using Wire
	userCommand := container.InitializeUserCommand(db)

	// Create root command
	rootCmd := &cobra.Command{
		Use:   "client-service",
		Short: "Smart Campus Client Service CLI",
		Long:  "Command line interface for Smart Campus Client Service",
	}

	// Add subcommands
	rootCmd.AddCommand(userCommand.GetCommand())

	// Execute
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}
