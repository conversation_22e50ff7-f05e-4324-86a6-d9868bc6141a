package entity

import (
	"time"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/domain/course/valueobject"
)

// Course represents a course entity in the domain
type Course struct {
	id          uuid.UUID
	code        valueobject.CourseCode
	name        string
	description string
	credits     int
	capacity    int
	enrolled    int
	status      CourseStatus
	instructor  uuid.UUID // Reference to User ID
	createdAt   time.Time
	updatedAt   time.Time
	version     int
}

// CourseStatus represents the status of a course
type CourseStatus int

const (
	CourseStatusDraft CourseStatus = iota
	CourseStatusActive
	CourseStatusFull
	CourseStatusClosed
)

// NewCourse creates a new course entity
func NewCourse(code valueobject.CourseCode, name, description string, credits, capacity int, instructor uuid.UUID) (*Course, error) {
	if name == "" {
		return nil, ErrInvalidCourseName
	}
	if credits <= 0 {
		return nil, ErrInvalidCredits
	}
	if capacity <= 0 {
		return nil, ErrInvalidCapacity
	}

	return &Course{
		id:          uuid.New(),
		code:        code,
		name:        name,
		description: description,
		credits:     credits,
		capacity:    capacity,
		enrolled:    0,
		status:      CourseStatusDraft,
		instructor:  instructor,
		createdAt:   time.Now(),
		updatedAt:   time.Now(),
		version:     1,
	}, nil
}

// ID returns the course ID
func (c *Course) ID() uuid.UUID {
	return c.id
}

// Code returns the course code
func (c *Course) Code() valueobject.CourseCode {
	return c.code
}

// Name returns the course name
func (c *Course) Name() string {
	return c.name
}

// Description returns the course description
func (c *Course) Description() string {
	return c.description
}

// Credits returns the course credits
func (c *Course) Credits() int {
	return c.credits
}

// Capacity returns the course capacity
func (c *Course) Capacity() int {
	return c.capacity
}

// Enrolled returns the number of enrolled students
func (c *Course) Enrolled() int {
	return c.enrolled
}

// Status returns the course status
func (c *Course) Status() CourseStatus {
	return c.status
}

// Instructor returns the instructor ID
func (c *Course) Instructor() uuid.UUID {
	return c.instructor
}

// CreatedAt returns the creation time
func (c *Course) CreatedAt() time.Time {
	return c.createdAt
}

// UpdatedAt returns the last update time
func (c *Course) UpdatedAt() time.Time {
	return c.updatedAt
}

// Version returns the entity version for optimistic locking
func (c *Course) Version() int {
	return c.version
}

// UpdateInfo updates the course information
func (c *Course) UpdateInfo(name, description string, credits int) error {
	if name == "" {
		return ErrInvalidCourseName
	}
	if credits <= 0 {
		return ErrInvalidCredits
	}

	c.name = name
	c.description = description
	c.credits = credits
	c.updatedAt = time.Now()
	c.version++
	return nil
}

// Activate activates the course
func (c *Course) Activate() error {
	if c.status != CourseStatusDraft {
		return ErrInvalidStatusTransition
	}

	c.status = CourseStatusActive
	c.updatedAt = time.Now()
	c.version++
	return nil
}

// Close closes the course
func (c *Course) Close() {
	c.status = CourseStatusClosed
	c.updatedAt = time.Now()
	c.version++
}

// Enroll enrolls a student in the course
func (c *Course) Enroll() error {
	if c.status != CourseStatusActive {
		return ErrCourseNotActive
	}
	if c.enrolled >= c.capacity {
		return ErrCourseCapacityExceeded
	}

	c.enrolled++
	if c.enrolled >= c.capacity {
		c.status = CourseStatusFull
	}
	c.updatedAt = time.Now()
	c.version++
	return nil
}

// Withdraw withdraws a student from the course
func (c *Course) Withdraw() error {
	if c.enrolled <= 0 {
		return ErrNoEnrolledStudents
	}

	c.enrolled--
	if c.status == CourseStatusFull && c.enrolled < c.capacity {
		c.status = CourseStatusActive
	}
	c.updatedAt = time.Now()
	c.version++
	return nil
}

// IsActive checks if the course is active
func (c *Course) IsActive() bool {
	return c.status == CourseStatusActive
}

// IsFull checks if the course is full
func (c *Course) IsFull() bool {
	return c.enrolled >= c.capacity
}

// AvailableSlots returns the number of available slots
func (c *Course) AvailableSlots() int {
	return c.capacity - c.enrolled
}

// ReconstructCourse reconstructs a course entity from persistence data
func ReconstructCourse(
	id uuid.UUID,
	code valueobject.CourseCode,
	name, description string,
	credits, capacity, enrolled int,
	status CourseStatus,
	instructor uuid.UUID,
	createdAt, updatedAt time.Time,
	version int,
) *Course {
	return &Course{
		id:          id,
		code:        code,
		name:        name,
		description: description,
		credits:     credits,
		capacity:    capacity,
		enrolled:    enrolled,
		status:      status,
		instructor:  instructor,
		createdAt:   createdAt,
		updatedAt:   updatedAt,
		version:     version,
	}
}
