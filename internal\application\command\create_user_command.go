package command

import (
	"context"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/domain/entity"
	"smart-campus/client-service/internal/domain/repository"
	"smart-campus/client-service/internal/domain/service"
	"smart-campus/client-service/internal/domain/valueobject"
)

// CreateUserCommand represents a command to create a user
type CreateUserCommand struct {
	Email string `json:"email" validate:"required,email"`
	Name  string `json:"name" validate:"required,min=2,max=100"`
}

// CreateUserCommandResult represents the result of creating a user
type CreateUserCommandResult struct {
	UserID uuid.UUID `json:"user_id"`
}

// CreateUserCommandHandler handles the create user command
type C<PERSON>U<PERSON><PERSON>ommandHandler struct {
	userRepo          repository.UserRepository
	userDomainService *service.UserDomainService
}

// NewCreateUserCommandHandler creates a new create user command handler
func NewCreateUserCommandHandler(
	userRepo repository.UserRepository,
	userDomainService *service.UserDomainService,
) *CreateUser<PERSON>ommandHandler {
	return &CreateUserCommandHandler{
		userRepo:          userRepo,
		userDomainService: userDomainService,
	}
}

// Handle handles the create user command
func (h *CreateUserCommandHandler) Handle(ctx context.Context, cmd CreateUserCommand) (*CreateUserCommandResult, error) {
	// Create email value object
	email, err := valueobject.NewEmail(cmd.Email)
	if err != nil {
		return nil, err
	}
	
	// Check if user can be created
	if err := h.userDomainService.CanCreateUser(ctx, email); err != nil {
		return nil, err
	}
	
	// Create user entity
	user, err := entity.NewUser(email, cmd.Name)
	if err != nil {
		return nil, err
	}
	
	// Save user
	if err := h.userRepo.Save(ctx, user); err != nil {
		return nil, err
	}
	
	return &CreateUserCommandResult{
		UserID: user.ID(),
	}, nil
}
