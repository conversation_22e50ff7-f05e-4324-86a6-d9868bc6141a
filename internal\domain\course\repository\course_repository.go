package repository

import (
	"context"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/domain/course/entity"
	"smart-campus/client-service/internal/domain/course/valueobject"
)

// CourseRepository defines the interface for course persistence operations
type CourseRepository interface {
	// Save saves a course entity
	Save(ctx context.Context, course *entity.Course) error
	
	// FindByID finds a course by ID
	FindByID(ctx context.Context, id uuid.UUID) (*entity.Course, error)
	
	// FindByCode finds a course by course code
	FindByCode(ctx context.Context, code valueobject.CourseCode) (*entity.Course, error)
	
	// FindByInstructor finds courses by instructor <PERSON>
	FindByInstructor(ctx context.Context, instructorID uuid.UUID) ([]*entity.Course, error)
	
	// FindAll finds all courses with pagination
	FindAll(ctx context.Context, offset, limit int) ([]*entity.Course, error)
	
	// FindActive finds all active courses with pagination
	FindActive(ctx context.Context, offset, limit int) ([]*entity.Course, error)
	
	// Update updates a course entity
	Update(ctx context.Context, course *entity.Course) error
	
	// Delete deletes a course by ID
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Exists checks if a course exists by code
	Exists(ctx context.Context, code valueobject.CourseCode) (bool, error)
	
	// Count returns the total number of courses
	Count(ctx context.Context) (int64, error)
	
	// CountByStatus returns the number of courses by status
	CountByStatus(ctx context.Context, status entity.CourseStatus) (int64, error)
}
