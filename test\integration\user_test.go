package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"smart-campus/client-service/internal/infrastructure/container"
	"smart-campus/client-service/internal/infrastructure/persistence"
	httpInterface "smart-campus/client-service/internal/interfaces/http"
)

type UserIntegrationTestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine
}

func (suite *UserIntegrationTestSuite) SetupSuite() {
	// Setup in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)
	
	// Run migrations
	err = persistence.AutoMigrate(db)
	suite.Require().NoError(err)
	
	suite.db = db
	
	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// Initialize dependencies
	userController := container.InitializeUserController(db)
	userController.RegisterRoutes(router)
	
	suite.router = router
}

func (suite *UserIntegrationTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *UserIntegrationTestSuite) TestCreateUser() {
	// Prepare request
	requestBody := map[string]interface{}{
		"email": "<EMAIL>",
		"name":  "Test User",
	}
	
	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/v1/users", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Execute request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	
	// Assert response
	assert.Equal(suite.T(), http.StatusCreated, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "user_id")
	assert.Contains(suite.T(), response, "message")
}

func (suite *UserIntegrationTestSuite) TestCreateUserInvalidEmail() {
	// Prepare request with invalid email
	requestBody := map[string]interface{}{
		"email": "invalid-email",
		"name":  "Test User",
	}
	
	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/v1/users", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	
	// Execute request
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	
	// Assert response
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

func (suite *UserIntegrationTestSuite) TestGetUser() {
	// First create a user
	requestBody := map[string]interface{}{
		"email": "<EMAIL>",
		"name":  "Get User",
	}
	
	jsonBody, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", "/api/v1/users", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	
	var createResponse map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &createResponse)
	userID := createResponse["user_id"].(string)
	
	// Now get the user
	req, _ = http.NewRequest("GET", fmt.Sprintf("/api/v1/users/%s", userID), nil)
	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	
	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "<EMAIL>", response["email"])
	assert.Equal(suite.T(), "Get User", response["name"])
}

func (suite *UserIntegrationTestSuite) TestListUsers() {
	// Create a few users first
	for i := 0; i < 3; i++ {
		requestBody := map[string]interface{}{
			"email": fmt.Sprintf("<EMAIL>", i),
			"name":  fmt.Sprintf("List User %d", i),
		}
		
		jsonBody, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", "/api/v1/users", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)
	}
	
	// Now list users
	req, _ := http.NewRequest("GET", "/api/v1/users?offset=0&limit=10", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	
	// Assert response
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "users")
	assert.Contains(suite.T(), response, "total_count")
	
	users := response["users"].([]interface{})
	assert.True(suite.T(), len(users) >= 3)
}

func TestUserIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(UserIntegrationTestSuite))
}
