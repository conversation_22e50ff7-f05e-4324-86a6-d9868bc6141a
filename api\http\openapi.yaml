openapi: 3.0.3
info:
  title: Smart Campus Client Service API
  description: API for managing users, courses, and enrollments in the smart campus system
  version: 1.0.0
  contact:
    name: Smart Campus Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080
    description: Development server
  - url: https://api.smartcampus.com
    description: Production server

paths:
  # Health Check
  /health:
    get:
      summary: Health check endpoint
      tags:
        - Health
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  timestamp:
                    type: string
                    format: date-time

  # User Management
  /api/v1/users:
    post:
      summary: Create a new user
      tags:
        - Users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

    get:
      summary: List users
      tags:
        - Users
      parameters:
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListUsersResponse'

  /api/v1/users/{id}:
    get:
      summary: Get user by ID
      tags:
        - Users
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      summary: Update user
      tags:
        - Users
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'

  # Course Management
  /api/v1/courses:
    post:
      summary: Create a new course
      tags:
        - Courses
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCourseRequest'
      responses:
        '201':
          description: Course created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCourseResponse'

    get:
      summary: List courses
      tags:
        - Courses
      parameters:
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [draft, active, full, closed]
      responses:
        '200':
          description: List of courses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListCoursesResponse'

components:
  schemas:
    # User Schemas
    CreateUserRequest:
      type: object
      required:
        - email
        - name
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        name:
          type: string
          minLength: 2
          maxLength: 100
          example: John Doe

    UpdateUserRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 100
          example: John Smith

    CreateUserResponse:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
        message:
          type: string
          example: User created successfully

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        name:
          type: string
        status:
          type: string
          enum: [active, inactive, suspended]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ListUsersResponse:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
        total_count:
          type: integer
        offset:
          type: integer
        limit:
          type: integer

    # Course Schemas
    CreateCourseRequest:
      type: object
      required:
        - code
        - name
        - credits
        - capacity
        - instructor_id
      properties:
        code:
          type: string
          pattern: '^[A-Z]{2,4}\d{3,4}$'
          example: CS101
        name:
          type: string
          minLength: 1
          maxLength: 200
          example: Introduction to Computer Science
        description:
          type: string
          maxLength: 1000
          example: Basic concepts of computer science and programming
        credits:
          type: integer
          minimum: 1
          maximum: 10
          example: 3
        capacity:
          type: integer
          minimum: 1
          maximum: 500
          example: 30
        instructor_id:
          type: string
          format: uuid

    CreateCourseResponse:
      type: object
      properties:
        course_id:
          type: string
          format: uuid
        message:
          type: string
          example: Course created successfully

    Course:
      type: object
      properties:
        id:
          type: string
          format: uuid
        code:
          type: string
        name:
          type: string
        description:
          type: string
        credits:
          type: integer
        capacity:
          type: integer
        enrolled:
          type: integer
        status:
          type: string
          enum: [draft, active, full, closed]
        instructor_id:
          type: string
          format: uuid
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    ListCoursesResponse:
      type: object
      properties:
        courses:
          type: array
          items:
            $ref: '#/components/schemas/Course'
        total_count:
          type: integer
        offset:
          type: integer
        limit:
          type: integer

    # Error Schemas
    Error:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
        details:
          type: string

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

tags:
  - name: Health
    description: Health check endpoints
  - name: Users
    description: User management operations
  - name: Courses
    description: Course management operations
