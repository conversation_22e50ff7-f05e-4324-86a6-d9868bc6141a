package entity

import "errors"

// Domain entity errors for course
var (
	ErrInvalidCourseName        = errors.New("invalid course name")
	ErrInvalidCredits           = errors.New("invalid credits")
	ErrInvalidCapacity          = errors.New("invalid capacity")
	ErrCourseNotFound           = errors.New("course not found")
	ErrCourseExists             = errors.New("course already exists")
	ErrInvalidStatusTransition  = errors.New("invalid status transition")
	ErrCourseNotActive          = errors.New("course is not active")
	ErrCourseCapacityExceeded   = errors.New("course capacity exceeded")
	ErrNoEnrolledStudents       = errors.New("no enrolled students")
)
