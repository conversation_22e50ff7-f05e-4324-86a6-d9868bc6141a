package service

import (
	"context"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/application/command"
	"smart-campus/client-service/internal/application/query"
)

// UserApplicationService provides application-level user operations
type UserApplicationService struct {
	createUserHandler   *command.CreateUserCommandHandler
	updateUserHandler   *command.UpdateUserCommandHandler
	getUserHandler      *query.GetUserQueryHandler
	listUsersHandler    *query.ListUsersQueryHandler
}

// NewUserApplicationService creates a new user application service
func NewUserApplicationService(
	createUserHandler *command.CreateUserCommandHandler,
	updateUserHandler *command.UpdateUserCommandHandler,
	getUserHandler *query.GetUserQueryHandler,
	listUsersHandler *query.ListUsersQueryHandler,
) *UserApplicationService {
	return &UserApplicationService{
		createUserHandler: createUserHandler,
		updateUserHandler: update<PERSON><PERSON><PERSON><PERSON><PERSON>,
		getU<PERSON><PERSON><PERSON><PERSON>:    get<PERSON><PERSON><PERSON><PERSON><PERSON>,
		listUsersHandler:  listUsersHand<PERSON>,
	}
}

// C<PERSON><PERSON><PERSON> creates a new user
func (s *UserApplicationService) CreateUser(ctx context.Context, cmd command.CreateUserCommand) (*command.CreateUserCommandResult, error) {
	return s.createUserHandler.Handle(ctx, cmd)
}

// UpdateUser updates an existing user
func (s *UserApplicationService) UpdateUser(ctx context.Context, cmd command.UpdateUserCommand) error {
	return s.updateUserHandler.Handle(ctx, cmd)
}

// GetUser gets a user by ID
func (s *UserApplicationService) GetUser(ctx context.Context, userID uuid.UUID) (*query.UserQueryResult, error) {
	return s.getUserHandler.Handle(ctx, query.GetUserQuery{UserID: userID})
}

// ListUsers lists users with pagination
func (s *UserApplicationService) ListUsers(ctx context.Context, offset, limit int) (*query.ListUsersQueryResult, error) {
	return s.listUsersHandler.Handle(ctx, query.ListUsersQuery{
		Offset: offset,
		Limit:  limit,
	})
}
