package valueobject

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewEmail(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "Valid email",
			input:       "<EMAIL>",
			expected:    "<EMAIL>",
			expectError: false,
		},
		{
			name:        "Valid email with uppercase",
			input:       "<EMAIL>",
			expected:    "<EMAIL>",
			expectError: false,
		},
		{
			name:        "Valid email with spaces",
			input:       "  <EMAIL>  ",
			expected:    "<EMAIL>",
			expectError: false,
		},
		{
			name:        "Invalid email - no @",
			input:       "testexample.com",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Invalid email - no domain",
			input:       "test@",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Invalid email - no local part",
			input:       "@example.com",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Invalid email - no TLD",
			input:       "test@example",
			expected:    "",
			expectError: true,
		},
		{
			name:        "Empty email",
			input:       "",
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			email, err := NewEmail(tt.input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, ErrInvalidEmail, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, email.Value())
				assert.Equal(t, tt.expected, email.String())
			}
		})
	}
}

func TestEmail_Domain(t *testing.T) {
	email, _ := NewEmail("<EMAIL>")
	assert.Equal(t, "example.com", email.Domain())
}

func TestEmail_LocalPart(t *testing.T) {
	email, _ := NewEmail("<EMAIL>")
	assert.Equal(t, "test", email.LocalPart())
}

func TestEmail_Equals(t *testing.T) {
	email1, _ := NewEmail("<EMAIL>")
	email2, _ := NewEmail("<EMAIL>")
	email3, _ := NewEmail("<EMAIL>")

	assert.True(t, email1.Equals(email2))
	assert.False(t, email1.Equals(email3))
}
