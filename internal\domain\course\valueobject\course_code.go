package valueobject

import (
	"errors"
	"regexp"
	"strings"
)

// CourseCode represents a course code value object
type CourseCode struct {
	value string
}

var (
	ErrInvalidCourseCode = errors.New("invalid course code format")
	// Course code format: 2-4 letters followed by 3-4 digits (e.g., CS101, MATH1001)
	courseCodeRegex = regexp.MustCompile(`^[A-Z]{2,4}\d{3,4}$`)
)

// NewCourseCode creates a new course code value object
func NewCourseCode(code string) (CourseCode, error) {
	code = strings.TrimSpace(strings.ToUpper(code))
	
	if !courseCodeRegex.MatchString(code) {
		return CourseCode{}, ErrInvalidCourseCode
	}
	
	return CourseCode{value: code}, nil
}

// Value returns the course code value
func (c CourseCode) Value() string {
	return c.value
}

// String returns the string representation of the course code
func (c CourseCode) String() string {
	return c.value
}

// Equals checks if two course codes are equal
func (c CourseCode) Equals(other CourseCode) bool {
	return c.value == other.value
}

// Subject returns the subject part of the course code (letters)
func (c CourseCode) Subject() string {
	for i, char := range c.value {
		if char >= '0' && char <= '9' {
			return c.value[:i]
		}
	}
	return c.value
}

// Number returns the number part of the course code (digits)
func (c CourseCode) Number() string {
	for i, char := range c.value {
		if char >= '0' && char <= '9' {
			return c.value[i:]
		}
	}
	return ""
}
