package persistence

import (
	"context"
	"errors"
	"time"

	"smart-campus/client-service/internal/domain/entity"
	"smart-campus/client-service/internal/domain/repository"
	"smart-campus/client-service/internal/domain/valueobject"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserModel represents the user database model
type UserModel struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email     string    `gorm:"uniqueIndex;not null"`
	Name      string    `gorm:"not null"`
	Status    int       `gorm:"not null;default:0"`
	CreatedAt int64     `gorm:"not null"`
	UpdatedAt int64     `gorm:"not null"`
	Version   int       `gorm:"not null;default:1"`
}

// TableName returns the table name for the user model
func (UserModel) TableName() string {
	return "users"
}

// UserRepositoryImpl implements the UserRepository interface
type UserRepositoryImpl struct {
	db *gorm.DB
}

// NewUserRepositoryImpl creates a new user repository implementation
func NewUserRepositoryImpl(db *gorm.DB) repository.UserRepository {
	return &UserRepositoryImpl{db: db}
}

// Save saves a user entity
func (r *UserRepositoryImpl) Save(ctx context.Context, user *entity.User) error {
	model := &UserModel{
		ID:        user.ID(),
		Email:     user.Email().Value(),
		Name:      user.Name(),
		Status:    int(user.Status()),
		CreatedAt: user.CreatedAt().Unix(),
		UpdatedAt: user.UpdatedAt().Unix(),
		Version:   user.Version(),
	}

	return r.db.WithContext(ctx).Create(model).Error
}

// FindByID finds a user by ID
func (r *UserRepositoryImpl) FindByID(ctx context.Context, id uuid.UUID) (*entity.User, error) {
	var model UserModel
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, entity.ErrUserNotFound
		}
		return nil, err
	}

	return r.modelToEntity(&model)
}

// FindByEmail finds a user by email
func (r *UserRepositoryImpl) FindByEmail(ctx context.Context, email valueobject.Email) (*entity.User, error) {
	var model UserModel
	err := r.db.WithContext(ctx).Where("email = ?", email.Value()).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, entity.ErrUserNotFound
		}
		return nil, err
	}

	return r.modelToEntity(&model)
}

// FindAll finds all users with pagination
func (r *UserRepositoryImpl) FindAll(ctx context.Context, offset, limit int) ([]*entity.User, error) {
	var models []UserModel
	err := r.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&models).Error
	if err != nil {
		return nil, err
	}

	users := make([]*entity.User, len(models))
	for i, model := range models {
		user, err := r.modelToEntity(&model)
		if err != nil {
			return nil, err
		}
		users[i] = user
	}

	return users, nil
}

// Update updates a user entity
func (r *UserRepositoryImpl) Update(ctx context.Context, user *entity.User) error {
	model := &UserModel{
		ID:        user.ID(),
		Email:     user.Email().Value(),
		Name:      user.Name(),
		Status:    int(user.Status()),
		CreatedAt: user.CreatedAt().Unix(),
		UpdatedAt: user.UpdatedAt().Unix(),
		Version:   user.Version(),
	}

	result := r.db.WithContext(ctx).Where("id = ? AND version = ?", user.ID(), user.Version()-1).Updates(model)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("optimistic lock error: user was modified by another process")
	}

	return nil
}

// Delete deletes a user by ID
func (r *UserRepositoryImpl) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&UserModel{}).Error
}

// Exists checks if a user exists by email
func (r *UserRepositoryImpl) Exists(ctx context.Context, email valueobject.Email) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&UserModel{}).Where("email = ?", email.Value()).Count(&count).Error
	return count > 0, err
}

// Count returns the total number of users
func (r *UserRepositoryImpl) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&UserModel{}).Count(&count).Error
	return count, err
}

// modelToEntity converts a database model to a domain entity
func (r *UserRepositoryImpl) modelToEntity(model *UserModel) (*entity.User, error) {
	email, err := valueobject.NewEmail(model.Email)
	if err != nil {
		return nil, err
	}

	return entity.ReconstructUser(
		model.ID,
		email,
		model.Name,
		entity.UserStatus(model.Status),
		time.Unix(model.CreatedAt, 0),
		time.Unix(model.UpdatedAt, 0),
		model.Version,
	), nil
}
