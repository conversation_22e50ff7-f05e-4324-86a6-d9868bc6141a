package repository

import (
	"context"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/domain/entity"
	"smart-campus/client-service/internal/domain/valueobject"
)

// UserRepository defines the interface for user persistence operations
type UserRepository interface {
	// Save saves a user entity
	Save(ctx context.Context, user *entity.User) error
	
	// FindByID finds a user by ID
	FindByID(ctx context.Context, id uuid.UUID) (*entity.User, error)
	
	// FindByEmail finds a user by email
	FindByEmail(ctx context.Context, email valueobject.Email) (*entity.User, error)
	
	// FindAll finds all users with pagination
	FindAll(ctx context.Context, offset, limit int) ([]*entity.User, error)
	
	// Update updates a user entity
	Update(ctx context.Context, user *entity.User) error
	
	// Delete deletes a user by ID
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Exists checks if a user exists by email
	Exists(ctx context.Context, email valueobject.Email) (bool, error)
	
	// Count returns the total number of users
	Count(ctx context.Context) (int64, error)
}
