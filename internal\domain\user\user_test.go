package entity

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"smart-campus/client-service/internal/domain/valueobject"
)

func TestNewUser(t *testing.T) {
	tests := []struct {
		name        string
		email       string
		userName    string
		expectError bool
	}{
		{
			name:        "Valid user creation",
			email:       "<EMAIL>",
			userName:    "Test User",
			expectError: false,
		},
		{
			name:        "Empty name should fail",
			email:       "<EMAIL>",
			userName:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			email, err := valueobject.NewEmail(tt.email)
			assert.NoError(t, err)

			user, err := NewUser(email, tt.userName)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, user)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, user)
				assert.Equal(t, email, user.Email())
				assert.Equal(t, tt.userName, user.Name())
				assert.Equal(t, UserStatusActive, user.Status())
				assert.True(t, user.IsActive())
				assert.Equal(t, 1, user.Version())
			}
		})
	}
}

func TestUser_UpdateName(t *testing.T) {
	email, _ := valueobject.NewEmail("<EMAIL>")
	user, _ := NewUser(email, "Original Name")
	originalVersion := user.Version()
	originalUpdatedAt := user.UpdatedAt()

	// Wait a bit to ensure timestamp difference
	time.Sleep(time.Millisecond)

	tests := []struct {
		name        string
		newName     string
		expectError bool
	}{
		{
			name:        "Valid name update",
			newName:     "Updated Name",
			expectError: false,
		},
		{
			name:        "Empty name should fail",
			newName:     "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := user.UpdateName(tt.newName)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.newName, user.Name())
				assert.Greater(t, user.Version(), originalVersion)
				assert.True(t, user.UpdatedAt().After(originalUpdatedAt))
			}
		})
	}
}

func TestUser_StatusTransitions(t *testing.T) {
	email, _ := valueobject.NewEmail("<EMAIL>")
	user, _ := NewUser(email, "Test User")

	// Initially active
	assert.True(t, user.IsActive())
	assert.Equal(t, UserStatusActive, user.Status())

	// Deactivate
	originalVersion := user.Version()
	user.Deactivate()
	assert.False(t, user.IsActive())
	assert.Equal(t, UserStatusInactive, user.Status())
	assert.Greater(t, user.Version(), originalVersion)

	// Suspend
	originalVersion = user.Version()
	user.Suspend()
	assert.False(t, user.IsActive())
	assert.Equal(t, UserStatusSuspended, user.Status())
	assert.Greater(t, user.Version(), originalVersion)

	// Activate again
	originalVersion = user.Version()
	user.Activate()
	assert.True(t, user.IsActive())
	assert.Equal(t, UserStatusActive, user.Status())
	assert.Greater(t, user.Version(), originalVersion)
}

func TestReconstructUser(t *testing.T) {
	id := uuid.New()
	email, _ := valueobject.NewEmail("<EMAIL>")
	name := "Test User"
	status := UserStatusActive
	createdAt := time.Now().Add(-time.Hour)
	updatedAt := time.Now()
	version := 5

	user := ReconstructUser(id, email, name, status, createdAt, updatedAt, version)

	assert.Equal(t, id, user.ID())
	assert.Equal(t, email, user.Email())
	assert.Equal(t, name, user.Name())
	assert.Equal(t, status, user.Status())
	assert.Equal(t, createdAt, user.CreatedAt())
	assert.Equal(t, updatedAt, user.UpdatedAt())
	assert.Equal(t, version, user.Version())
}
