package query

import (
	"context"
	"time"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/domain/repository"
)

// GetUserQuery represents a query to get a user
type GetUserQuery struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

// UserQueryResult represents the result of a user query
type UserQueryResult struct {
	ID        uuid.UUID `json:"id"`
	Email     string    `json:"email"`
	Name      string    `json:"name"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// GetUserQueryHandler handles the get user query
type GetUserQueryHandler struct {
	userRepo repository.UserRepository
}

// NewGetUserQueryHandler creates a new get user query handler
func NewGetUserQueryHandler(userRepo repository.UserRepository) *GetUserQueryHandler {
	return &GetUserQueryHandler{
		userRepo: userRepo,
	}
}

// <PERSON><PERSON> handles the get user query
func (h *GetUserQueryHandler) Handle(ctx context.Context, query GetUserQuery) (*UserQueryResult, error) {
	user, err := h.userRepo.FindByID(ctx, query.UserID)
	if err != nil {
		return nil, err
	}
	
	var status string
	switch user.Status() {
	case 0: // UserStatusActive
		status = "active"
	case 1: // UserStatusInactive
		status = "inactive"
	case 2: // UserStatusSuspended
		status = "suspended"
	default:
		status = "unknown"
	}
	
	return &UserQueryResult{
		ID:        user.ID(),
		Email:     user.Email().Value(),
		Name:      user.Name(),
		Status:    status,
		CreatedAt: user.CreatedAt(),
		UpdatedAt: user.UpdatedAt(),
	}, nil
}
