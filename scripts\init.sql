-- Initialize database for Smart Campus Client Service

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (will be managed by GORM migrations, but this ensures basic structure)
-- This is just for reference, actual table creation is handled by GORM AutoMigrate

-- You can add any initial data or additional database setup here
-- For example:

-- INSERT INTO users (id, email, name, status, created_at, updated_at, version) 
-- VALUES (uuid_generate_v4(), '<EMAIL>', 'System Admin', 0, extract(epoch from now()), extract(epoch from now()), 1);

-- Create indexes for better performance (if not handled by GORM)
-- CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
-- CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
-- CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
