# Smart Campus Client Service

基于DDD（领域驱动设计）+ 六边形架构的Go微服务开发框架。

## 架构概述

本项目采用了DDD（Domain-Driven Design）和六边形架构（Hexagonal Architecture）的设计模式，确保了代码的可维护性、可测试性和业务逻辑的清晰分离。

### 核心架构原则

1. **领域驱动设计（DDD）**：以业务领域为核心，将复杂的业务逻辑封装在领域层
2. **六边形架构**：将应用程序分为内部（业务逻辑）和外部（基础设施），通过端口和适配器进行交互
3. **依赖倒置**：高层模块不依赖低层模块，都依赖于抽象
4. **关注点分离**：每一层都有明确的职责和边界

## 项目结构

```
smart-campus/client-service/
├── cmd/                          # 应用程序入口点
│   ├── server/                   # HTTP服务器入口
│   └── cli/                      # CLI工具入口
├── internal/                     # 内部应用代码
│   ├── domain/                   # 领域层（核心业务逻辑）
│   │   ├── entity/               # 实体
│   │   ├── valueobject/          # 值对象
│   │   ├── service/              # 领域服务
│   │   └── repository/           # 仓储接口
│   ├── application/              # 应用层（用例编排）
│   │   ├── service/              # 应用服务
│   │   ├── usecase/              # 用例
│   │   ├── command/              # 命令处理器
│   │   └── query/                # 查询处理器
│   ├── infrastructure/           # 基础设施层（外部依赖）
│   │   ├── persistence/          # 数据持久化
│   │   ├── external/             # 外部服务
│   │   ├── messaging/            # 消息队列
│   │   └── container/            # 依赖注入容器
│   └── interfaces/               # 接口适配器层（外部接口）
│       ├── http/                 # HTTP接口
│       ├── grpc/                 # gRPC接口
│       └── cli/                  # CLI接口
├── pkg/                          # 公共库
│   ├── config/                   # 配置管理
│   ├── logger/                   # 日志
│   ├── errors/                   # 错误处理
│   └── utils/                    # 工具函数
├── config/                       # 配置文件
├── docs/                         # 文档
├── scripts/                      # 脚本
└── test/                         # 测试
```

## 快速开始

### 环境要求

- Go 1.21+
- PostgreSQL 12+
- Make

### 安装依赖

```bash
make deps
```

### 配置数据库

1. 创建PostgreSQL数据库：
```sql
CREATE DATABASE smart_campus;
```

2. 修改配置文件 `config/config.yaml`：
```yaml
database:
  host: "localhost"
  port: 5432
  user: "your_username"
  password: "your_password"
  dbname: "smart_campus"
  sslmode: "disable"
```

### 生成依赖注入代码

```bash
make wire
```

### 运行应用

#### HTTP服务器
```bash
make run
# 或者
go run cmd/server/main.go
```

#### CLI工具
```bash
go run cmd/cli/main.go user --help
```

### 构建

```bash
make build
```

## API文档

### 用户管理API

#### 创建用户
```http
POST /api/v1/users
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "John Doe"
}
```

#### 获取用户
```http
GET /api/v1/users/{id}
```

#### 更新用户
```http
PUT /api/v1/users/{id}
Content-Type: application/json

{
  "name": "Jane Doe"
}
```

#### 用户列表
```http
GET /api/v1/users?offset=0&limit=20
```

## CLI使用

### 用户管理命令

```bash
# 创建用户
./bin/client-service <NAME_EMAIL> "John Doe"

# 获取用户
./bin/client-service user get {user-id}

# 更新用户
./bin/client-service user update {user-id} "Jane Doe"

# 用户列表
./bin/client-service user list --offset=0 --limit=20
```

## 测试

```bash
# 运行所有测试
make test

# 运行测试并生成覆盖率报告
make test-coverage
```

## 开发

### 添加新的业务功能

1. **定义领域实体和值对象**（`internal/domain/entity/`, `internal/domain/valueobject/`）
2. **创建仓储接口**（`internal/domain/repository/`）
3. **实现领域服务**（`internal/domain/service/`）
4. **创建应用服务和用例**（`internal/application/`）
5. **实现基础设施**（`internal/infrastructure/`）
6. **添加接口适配器**（`internal/interfaces/`）
7. **更新依赖注入配置**（`internal/infrastructure/container/wire.go`）

### 代码规范

- 遵循Go官方代码规范
- 使用`make fmt`格式化代码
- 使用`make lint`检查代码质量
- 编写单元测试和集成测试

## 部署

### Docker部署

```bash
make docker-build
docker run -p 8080:8080 client-service
```

### 环境变量配置

可以通过环境变量覆盖配置文件中的设置：

```bash
export SERVER_PORT=9090
export DATABASE_HOST=db.example.com
export LOGGER_LEVEL=debug
```

## 贡献

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 架构详细说明

详细的架构说明请参考 [docs/ARCHITECTURE.md](docs/ARCHITECTURE.md)

## 许可证

MIT License
