package service

import (
	"context"
	"errors"

	"smart-campus/client-service/internal/domain/entity"
	"smart-campus/client-service/internal/domain/repository"
	"smart-campus/client-service/internal/domain/valueobject"
)

var (
	ErrUserAlreadyExists = errors.New("user already exists")
)

// UserDomainService provides domain-specific business logic for users
type UserDomainService struct {
	userRepo repository.UserRepository
}

// NewUserDomainService creates a new user domain service
func NewUserDomainService(userRepo repository.UserRepository) *UserDomainService {
	return &UserDomainService{
		userRepo: userRepo,
	}
}

// CanCreateUser checks if a user can be created with the given email
func (s *UserDomainService) CanCreateUser(ctx context.Context, email valueobject.Email) error {
	exists, err := s.userRepo.Exists(ctx, email)
	if err != nil {
		return err
	}
	
	if exists {
		return ErrUserAlreadyExists
	}
	
	return nil
}

// ValidateUserForUpdate validates a user before update
func (s *UserDomainService) ValidateUserForUpdate(ctx context.Context, user *entity.User) error {
	// Add domain-specific validation logic here
	// For example, check business rules, constraints, etc.
	
	if !user.IsActive() {
		return errors.New("cannot update inactive user")
	}
	
	return nil
}

// CanDeleteUser checks if a user can be deleted
func (s *UserDomainService) CanDeleteUser(ctx context.Context, user *entity.User) error {
	// Add domain-specific deletion rules here
	// For example, check if user has active sessions, pending orders, etc.
	
	if user.IsActive() {
		return errors.New("cannot delete active user, deactivate first")
	}
	
	return nil
}
