package entity

import (
	"time"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/domain/valueobject"
)

// User represents a user entity in the domain
type User struct {
	id        uuid.UUID
	email     valueobject.Email
	name      string
	status    UserStatus
	createdAt time.Time
	updatedAt time.Time
	version   int
}

// UserStatus represents the status of a user
type UserStatus int

const (
	UserStatusActive UserStatus = iota
	UserStatusInactive
	UserStatusSuspended
)

// New<PERSON><PERSON> creates a new user entity
func NewUser(email valueobject.Email, name string) (*User, error) {
	if name == "" {
		return nil, ErrInvalidUserName
	}

	return &User{
		id:        uuid.New(),
		email:     email,
		name:      name,
		status:    UserStatusActive,
		createdAt: time.Now(),
		updatedAt: time.Now(),
		version:   1,
	}, nil
}

// ID returns the user ID
func (u *User) ID() uuid.UUID {
	return u.id
}

// Email returns the user email
func (u *User) Email() valueobject.Email {
	return u.email
}

// Name returns the user name
func (u *User) Name() string {
	return u.name
}

// Status returns the user status
func (u *User) Status() UserStatus {
	return u.status
}

// CreatedAt returns the creation time
func (u *User) CreatedAt() time.Time {
	return u.createdAt
}

// UpdatedAt returns the last update time
func (u *User) UpdatedAt() time.Time {
	return u.updatedAt
}

// Version returns the entity version for optimistic locking
func (u *User) Version() int {
	return u.version
}

// UpdateName updates the user name
func (u *User) UpdateName(name string) error {
	if name == "" {
		return ErrInvalidUserName
	}
	
	u.name = name
	u.updatedAt = time.Now()
	u.version++
	return nil
}

// Activate activates the user
func (u *User) Activate() {
	u.status = UserStatusActive
	u.updatedAt = time.Now()
	u.version++
}

// Deactivate deactivates the user
func (u *User) Deactivate() {
	u.status = UserStatusInactive
	u.updatedAt = time.Now()
	u.version++
}

// Suspend suspends the user
func (u *User) Suspend() {
	u.status = UserStatusSuspended
	u.updatedAt = time.Now()
	u.version++
}

// IsActive checks if the user is active
func (u *User) IsActive() bool {
	return u.status == UserStatusActive
}

// Reconstruct reconstructs a user entity from persistence data
func ReconstructUser(id uuid.UUID, email valueobject.Email, name string, status UserStatus, createdAt, updatedAt time.Time, version int) *User {
	return &User{
		id:        id,
		email:     email,
		name:      name,
		status:    status,
		createdAt: createdAt,
		updatedAt: updatedAt,
		version:   version,
	}
}
