# 架构详细说明

## 概述

本项目采用DDD（领域驱动设计）+ 六边形架构的设计模式，旨在构建一个高度可维护、可测试和可扩展的微服务架构。

## 架构层次

### 1. 领域层 (Domain Layer)

领域层是整个应用的核心，包含了业务的核心逻辑和规则。

#### 目录结构
```
internal/domain/
├── entity/          # 实体
├── valueobject/     # 值对象
├── service/         # 领域服务
└── repository/      # 仓储接口
```

#### 组件说明

**实体 (Entity)**
- 位置：`internal/domain/entity/`
- 职责：表示具有唯一标识的业务对象
- 特点：
  - 具有唯一ID
  - 封装业务规则和行为
  - 生命周期管理
  - 状态变更方法

**值对象 (Value Object)**
- 位置：`internal/domain/valueobject/`
- 职责：表示没有唯一标识的业务概念
- 特点：
  - 不可变性
  - 值相等性
  - 自验证
  - 无副作用

**领域服务 (Domain Service)**
- 位置：`internal/domain/service/`
- 职责：处理跨实体的业务逻辑
- 特点：
  - 无状态
  - 纯业务逻辑
  - 协调多个实体

**仓储接口 (Repository Interface)**
- 位置：`internal/domain/repository/`
- 职责：定义数据访问的抽象接口
- 特点：
  - 面向领域的接口设计
  - 隐藏持久化细节
  - 支持领域查询

### 2. 应用层 (Application Layer)

应用层负责协调领域对象来完成用户的用例。

#### 目录结构
```
internal/application/
├── service/         # 应用服务
├── usecase/         # 用例
├── command/         # 命令处理器
└── query/           # 查询处理器
```

#### 组件说明

**应用服务 (Application Service)**
- 位置：`internal/application/service/`
- 职责：协调领域对象执行业务用例
- 特点：
  - 事务边界
  - 用例编排
  - 外部接口适配

**命令处理器 (Command Handler)**
- 位置：`internal/application/command/`
- 职责：处理修改系统状态的操作
- 特点：
  - CQRS模式
  - 命令验证
  - 业务规则执行

**查询处理器 (Query Handler)**
- 位置：`internal/application/query/`
- 职责：处理数据查询操作
- 特点：
  - 只读操作
  - 数据投影
  - 性能优化

### 3. 基础设施层 (Infrastructure Layer)

基础设施层提供技术实现，支撑上层业务逻辑。

#### 目录结构
```
internal/infrastructure/
├── persistence/     # 数据持久化
├── external/        # 外部服务
├── messaging/       # 消息队列
└── container/       # 依赖注入
```

#### 组件说明

**数据持久化 (Persistence)**
- 位置：`internal/infrastructure/persistence/`
- 职责：实现仓储接口，提供数据存储能力
- 特点：
  - ORM映射
  - 数据库连接管理
  - 事务处理

**外部服务 (External Services)**
- 位置：`internal/infrastructure/external/`
- 职责：与外部系统集成
- 特点：
  - HTTP客户端
  - 第三方API调用
  - 服务发现

**消息队列 (Messaging)**
- 位置：`internal/infrastructure/messaging/`
- 职责：处理异步消息和事件
- 特点：
  - 事件发布
  - 消息订阅
  - 异步处理

**依赖注入容器 (Container)**
- 位置：`internal/infrastructure/container/`
- 职责：管理依赖关系和对象生命周期
- 特点：
  - Wire框架
  - 自动装配
  - 生命周期管理

### 4. 接口适配器层 (Interface Adapters)

接口适配器层负责将外部请求转换为应用层能够处理的格式。

#### 目录结构
```
internal/interfaces/
├── http/            # HTTP接口
├── grpc/            # gRPC接口
└── cli/             # CLI接口
```

#### 组件说明

**HTTP接口 (HTTP Interface)**
- 位置：`internal/interfaces/http/`
- 职责：提供RESTful API
- 特点：
  - 路由管理
  - 请求验证
  - 响应格式化
  - 中间件支持

**gRPC接口 (gRPC Interface)**
- 位置：`internal/interfaces/grpc/`
- 职责：提供高性能RPC服务
- 特点：
  - Protocol Buffers
  - 流式处理
  - 服务发现

**CLI接口 (CLI Interface)**
- 位置：`internal/interfaces/cli/`
- 职责：提供命令行工具
- 特点：
  - 命令解析
  - 参数验证
  - 输出格式化

## 公共库 (pkg/)

公共库提供跨层使用的通用功能。

### 目录结构
```
pkg/
├── config/          # 配置管理
├── logger/          # 日志处理
├── errors/          # 错误处理
└── utils/           # 工具函数
```

### 组件说明

**配置管理 (Config)**
- 职责：统一配置管理
- 特点：
  - 多环境支持
  - 环境变量覆盖
  - 配置验证

**日志处理 (Logger)**
- 职责：结构化日志记录
- 特点：
  - 多级别日志
  - 结构化输出
  - 性能优化

**错误处理 (Errors)**
- 职责：统一错误处理机制
- 特点：
  - 错误分类
  - 错误链
  - HTTP状态码映射

## 依赖关系

### 依赖方向
```
Interfaces → Application → Domain
     ↓            ↓
Infrastructure ←←←←←
```

### 关键原则

1. **依赖倒置**：内层定义接口，外层实现接口
2. **单向依赖**：外层依赖内层，内层不依赖外层
3. **接口隔离**：每个接口只包含必要的方法
4. **开闭原则**：对扩展开放，对修改关闭

## 数据流

### 请求处理流程
```
HTTP Request → Controller → Application Service → Domain Service → Repository → Database
                    ↓              ↓               ↓            ↓
HTTP Response ← DTO Format ← Use Case Result ← Domain Entity ← Data Model
```

### 事件处理流程
```
Domain Event → Event Publisher → Message Queue → Event Handler → Side Effects
```

## 测试策略

### 测试金字塔
```
E2E Tests (少量)
    ↓
Integration Tests (适量)
    ↓
Unit Tests (大量)
```

### 测试分层

**单元测试**
- 领域实体和值对象
- 领域服务
- 应用服务
- 工具函数

**集成测试**
- 仓储实现
- 外部服务集成
- 数据库操作

**端到端测试**
- API接口
- 完整业务流程
- 用户场景

## 扩展指南

### 添加新功能

1. **分析业务需求**：确定领域概念和边界
2. **设计领域模型**：创建实体、值对象和领域服务
3. **定义应用用例**：创建命令和查询处理器
4. **实现基础设施**：添加仓储实现和外部服务
5. **创建接口适配器**：添加HTTP/gRPC/CLI接口
6. **更新依赖注入**：配置Wire依赖关系
7. **编写测试**：添加单元测试和集成测试

### 性能优化

1. **数据库优化**：索引、查询优化、连接池
2. **缓存策略**：Redis、内存缓存
3. **异步处理**：消息队列、事件驱动
4. **监控告警**：指标收集、链路追踪

### 部署策略

1. **容器化**：Docker镜像构建
2. **服务发现**：Consul、Kubernetes
3. **负载均衡**：Nginx、HAProxy
4. **配置管理**：ConfigMap、Secret
