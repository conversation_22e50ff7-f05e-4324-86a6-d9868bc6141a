package command

import (
	"context"

	"github.com/google/uuid"
	"smart-campus/client-service/internal/domain/repository"
	"smart-campus/client-service/internal/domain/service"
)

// UpdateUserCommand represents a command to update a user
type UpdateUserCommand struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
	Name   string    `json:"name" validate:"required,min=2,max=100"`
}

// UpdateUserCommandHandler handles the update user command
type UpdateUserCommandHandler struct {
	userRepo          repository.UserRepository
	userDomainService *service.UserDomainService
}

// NewUpdateUserCommandHandler creates a new update user command handler
func NewUpdateUserCommandHandler(
	userRepo repository.UserRepository,
	userDomainService *service.UserDomainService,
) *UpdateUserCommandHandler {
	return &UpdateUserCommandHandler{
		userRepo:          userRepo,
		userDomainService: userDomainService,
	}
}

// <PERSON><PERSON> handles the update user command
func (h *UpdateUserCommandHandler) Handle(ctx context.Context, cmd UpdateUserCommand) error {
	// Find user
	user, err := h.userRepo.FindByID(ctx, cmd.UserID)
	if err != nil {
		return err
	}
	
	// Validate user for update
	if err := h.userDomainService.ValidateUserForUpdate(ctx, user); err != nil {
		return err
	}
	
	// Update user
	if err := user.UpdateName(cmd.Name); err != nil {
		return err
	}
	
	// Save user
	return h.userRepo.Update(ctx, user)
}
