//go:build wireinject
// +build wireinject

package container

import (
	"github.com/google/wire"
	"gorm.io/gorm"
	"smart-campus/client-service/internal/application/command"
	"smart-campus/client-service/internal/application/query"
	"smart-campus/client-service/internal/application/service"
	"smart-campus/client-service/internal/domain/repository"
	domainService "smart-campus/client-service/internal/domain/service"
	"smart-campus/client-service/internal/infrastructure/persistence"
	"smart-campus/client-service/internal/interfaces/cli"
	"smart-campus/client-service/internal/interfaces/http"
	"smart-campus/client-service/pkg/config"
)

// ProviderSet is the Wire provider set for dependency injection
var ProviderSet = wire.NewSet(
	// Infrastructure
	persistence.NewUserRepositoryImpl,
	wire.Bind(new(repository.UserRepository), new(*persistence.UserRepositoryImpl)),

	// Domain Services
	domainService.NewUserDomainService,

	// Application Services
	command.NewCreateUserCommandHandler,
	command.NewUpdateUser<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	query.NewGetUser<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	query.NewListUsersQueryHandler,
	service.NewUserApplicationService,

	// Interface Adapters
	http.NewUserController,
	cli.NewUserCommand,
)

// InitializeUserController initializes the user controller with all dependencies
func InitializeUserController(db *gorm.DB) *http.UserController {
	wire.Build(ProviderSet)
	return &http.UserController{}
}

// InitializeUserCommand initializes the user CLI command with all dependencies
func InitializeUserCommand(db *gorm.DB) *cli.UserCommand {
	wire.Build(ProviderSet)
	return &cli.UserCommand{}
}

// InitializeUserApplicationService initializes the user application service with all dependencies
func InitializeUserApplicationService(db *gorm.DB) *service.UserApplicationService {
	wire.Build(ProviderSet)
	return &service.UserApplicationService{}
}
